
'use client';

import dynamic from 'next/dynamic';
import {
  useEffect, useState,
} from 'react';
import { useSession } from 'next-auth/react';
import { useUpdateWatchlist } from '@/common/hooks/useUpdateWatchlist';
import { useGetWatchlist } from '@/app/watchlist/useGetWatchlist';
import { StockRatingModal } from '@/common/components/organisms/modal';
import CompanyLogo from './images/coinshares-logo-white.svg'
import CompanyCover from './images/cs-hero-cover.jpg'
import {
  CompanySize,
  Region,
  TypeOfStock,
  RevenueStage,
} from "@/app/company/[slug]/types";

const TitleSection = dynamic(() => import("@/app/company/[slug]/sections/TitleSection").then(m => m), { ssr: false });
const StocksSection = dynamic(() => import("@/app/company/[slug]/sections/StocksSection/StocksSection").then(m => m), { ssr: false });
const Footer = dynamic(() => import("../../[slug]/sections").then(m => m.Footer), { ssr: false });
const mockData = {
  "data": {
    "showcase_id": 3143,
    "enable_curation_ai": false,
    "title": "CoinShares",
    "description": "CoinShares is a European digital asset manager offering exposure to cryptocurrencies via regulated investment vehicles. The firm was early to the market with crypto exchange-traded products (ETPs), and now oversees a diversified $5.2 billion platform spanning Bitcoin ETPs, thematic equity indices, and a US-listed ETF suite. Its operations also include proprietary trading, lending, and staking within its capital markets infrastructure.",
    "type": "text",
    "private_showcase": false,
    "logo": CompanyLogo,
    "showcase_heading": "Leveraged play on Bitcoin",
    "investor_relations_link": "https://app.curationconnect.com/account/signup",
    "investor_relations_label": "+Watchlist",
    "graph_link": "",
    "contact_email": "<EMAIL>",
    "slug": "CoinShares",
    "status": "live",
    "teaser_video": "https://www.youtube.com/watch?v=cGGbJVCdOg4",
    "teaser_video_iframe_url": "https://www.youtube.com/embed/cGGbJVCdOg4?si=LHKuJEexRSvADzoe",
    "teaser_video_label": "Watch Video",
    "teaser_video_title": null,
    "teaser_video_caption": null,
    "hero_caption": "Learn more about Coinshares",
    "hero_cta_label": "+Watchlist",
    "hero_cta_link": "https://app.curationconnect.com/account/signup",
    "disclaimer": null,
    "discovery_cover_image": CompanyCover.src,
    "discovery_caption": "Leveraged play on Bitcoin",
    "updated_at": "2025-05-14 10:51:41",
    "showcase_stocks": {
      "id": 3617,
      "showcase_id": 7180,
      "exchange": "Nasdaq Stockholm",
      "symbol": "CS",
      "price": 0.3,
      "change_percent": 0,
      "average_volume": 367220,
      "marketcap": ********,
      "pe_ratio": 0,
      "raise_amount": null,
      "equity_offered": null,
      "valuation": null,
      "revenue": null,
      "market_opportunity": null,
      "raise_amount_currency": null,
      "valuation_currency": null,
      "revenue_currency": null,
      "market_opportunity_currency": null,
      "target_price": null,
      "price_target_disclaimer": null,
    },
    "stock_symbol": "",
    "exchange": "",
    "symbol": "",
    "price": 0.3,
    "change_percent": 0,
    "average_volume": 367220,
    "marketcap": ********,
    "pe_ratio": 0,
    "raise_amount": null,
    "equity_offered": null,
    "valuation": null,
    "revenue": null,
    "market_opportunity": null,
    "raise_amount_currency": null,
    "valuation_currency": null,
    "revenue_currency": null,
    "market_opportunity_currency": null,
    "target_price": null,
    "price_target_disclaimer": null,
    "target_price_change_percentage": null,
    "buy": [
      {
        "id": 4975,
        "showcase_id": 7180,
        "type": "buy",
        "title": "Leader in European Crypto ETPs",
        "caption": "Largest net inflows in Europe, strong brand and early regulatory advantage.",
      },
      {
        "id": 4976,
        "showcase_id": 7180,
        "type": "buy",
        "title": "Profitable Platform Despite Volatility",
        "caption": "High EBITDA margins sustained even in weak markets through diversified income streams.",
      },
      {
        "id": 4977,
        "showcase_id": 7180,
        "type": "buy",
        "title": "Strategic U.S. Expansion Potential",
        "caption": "Active groundwork in U.S. ETFs and distribution aligns with regulatory momentum.",
      },
    ],
    "sell": [
      {
        "id": 4978,
        "showcase_id": 7180,
        "type": "sell",
        "title": "Regulatory Change",
        "caption": "New EU or U.S. rules could restrict products or raise compliance costs.",
      },
      {
        "id": 4979,
        "showcase_id": 7180,
        "type": "sell",
        "title": "Product Concentration",
        "caption": "Heavy reliance on XBT platform and Bitcoin limits diversification benefits.",
      },
      {
        "id": 4980,
        "showcase_id": 7180,
        "type": "sell",
        "title": "Treasury Volatility",
        "caption": "Unrealized crypto losses still impact investor perception and headline profit.",
      },
    ],
    "industries": [
      {
        "id": 1489,
        "industry_id": 4,
        "name": "Finance",
      },
    ],
    "tag": {
      "id": 696,
      "showcase_id": 7180,
      "company_size": "medium",
      "region": "europe",
      "type_of_stock": "cyclical",
      "revenue_stage": "pre_revenue",
      "mag7": false,
      "buy_back": false,
      "income": false,
      "debt": false,
      "management_ownership_more_than_5_percent": false,
    },
  },
};

const CustomShowcasePage = () => {
  const [watchlistCount, setWatchlistCount] = useState(0);
  const [isRatingModalOpen, setIsRatingModalOpen] = useState(false);
  const [ratingModalSource, setRatingModalSource] = useState('');
  const [showStocksSection] = useState(false);
  
  const {
    data: session, status,
  } = useSession();
  const {
    onSyncWatchlist,
  } = useUpdateWatchlist()
  const {
    mutateWatchlist,
    watchlist: watchlistList,
  } = useGetWatchlist()

  const { data } = mockData;

  const reFreshWatchlistCount = () => {
    const localWatchlist = localStorage.getItem('watchlist')
    const list = localWatchlist ? JSON.parse(localWatchlist) : []
    setWatchlistCount(list.length)
  }

  useEffect(() => {
    const asyncSyncWatchlist = async () => {
      if (status === "loading") {
        return
      }
      if (status === "authenticated" && session?.user?.role === "subscriber") {
        const localWatchlist = localStorage.getItem('watchlist')
        const list = localWatchlist ? JSON.parse(localWatchlist) : []
        if (list.length) {
          await onSyncWatchlist(session.user.email)
          mutateWatchlist()
        }
      }
    }
    asyncSyncWatchlist()
  }, [session, status, onSyncWatchlist, mutateWatchlist])

  useEffect(() => {
    if (status === "loading") {
      return
    }
    if (status === "authenticated" && session?.user?.role === "subscriber") {
      setWatchlistCount(watchlistList.length)
      return
    }
    reFreshWatchlistCount()
  }, [session, status, watchlistList])

  const mapCompanySize = (size: string) => {
    switch (size) {
    case 'micro': return CompanySize.MICRO;
    case 'small': return CompanySize.SMALL;
    case 'medium': return CompanySize.MEDIUM;
    case 'large': return CompanySize.LARGE;
    case 'mega': return CompanySize.MEGA;
    default: return CompanySize.MICRO;
    }
  };

  const mapRegion = (region: string) => {
    switch (region) {
    case 'uk': return Region.UK;
    case 'usa': return Region.USA;
    case 'europe': return Region.EUROPE;
    case 'canada': return Region.CANADA;
    case 'row': return Region.ROW;
    default: return Region.UK;
    }
  };

  const mapTypeOfStock = (type: string) => {
    switch (type) {
    case 'growth': return TypeOfStock.GROWTH;
    case 'value': return TypeOfStock.VALUE;
    case 'cyclical': return TypeOfStock.CYCLICAL;
    case 'defensive': return TypeOfStock.DEFENSIVE;
    default: return TypeOfStock.CYCLICAL;
    }
  };

  const mapRevenueStage = (stage: string) => {
    switch (stage) {
    case 'pre_revenue': return RevenueStage.PRE_REVENUE;
    case 'post_revenue': return RevenueStage.POST_REVENUE;
    case 'profitable': return RevenueStage.PROFITABLE;
    default: return RevenueStage.POST_REVENUE;
    }
  };

  const handleAddToWatchlist = () => {
    const currentWatchlist = JSON.parse(localStorage.getItem('watchlist') || '[]')
    localStorage.setItem('watchlist', JSON.stringify([
      ...currentWatchlist,
      {
        slug: data.slug,
        title: data.title || '',
        logo: data.logo,
        watchlistedPrice: data.price || 0,
        followedSince: new Date().toString(),
      },
    ]))
    reFreshWatchlistCount()
  };

  const handleRateStock = (source: string) => {
    setRatingModalSource(source);
    setIsRatingModalOpen(true);
  };

  const titleSectionProps = {
    title: data.showcase_heading || data.title,
    subtitle: data.description,
    updatedAt: data.updated_at,
    watchlistCount: watchlistCount,
    price: data.price?.toString() || "0",
    industries: data.industries,
    tags: {
      buy_back: data.tag.buy_back,
      company_size: mapCompanySize(data.tag.company_size),
      debt: data.tag.debt,
      id: data.tag.id,
      income: data.tag.income,
      mag7: data.tag.mag7,
      management_ownership_more_than_5_percent: data.tag.management_ownership_more_than_5_percent,
      region: mapRegion(data.tag.region),
      revenue_stage: mapRevenueStage(data.tag.revenue_stage),
      showcase_id: data.tag.showcase_id,
      type_of_stock: mapTypeOfStock(data.tag.type_of_stock),
    },
    changePercent: data.change_percent?.toString() || "0",
    graphLink: '',
    teaserVideoLink: data.teaser_video_iframe_url,
    teaserVideoLabel: data.teaser_video_label,
    handleAddToWatchlist: () => handleAddToWatchlist(),
    handleRateStock: handleRateStock,
    rateStockLabel: () => "Rate the Stock",
    showRateStockButton: true,
    logo: data.logo,
    heroCTALink: data.investor_relations_link,
    heroCTALabel: data.investor_relations_label,
    slug: data.slug,
    discoveryCoverImage: data.discovery_cover_image,
    privateShowcase: data.private_showcase,
    companyName: data.title,
    enableCurationAI: data.enable_curation_ai,
    teaserVideoTitle: data.teaser_video_title || "",
    teaserVideoCaption: data.teaser_video_caption || data.hero_caption,
    exchange: data.exchange,
    symbol: data.symbol,
    isWatchlisted: false,
    setIsWatchlisted: () => { },
    primaryCTALabel: () => "+ Watchlist",
    buy: data.buy,
    sell: data.sell,
    prompts: [
    ],
  };

  const stocksSectionProps = {
    privateShowcase: data.private_showcase,
    raiseAmount: data.raise_amount ? String(data.raise_amount) : "0",
    raiseAmountCurrency: data.raise_amount_currency || "$",
    equityOffered: data.equity_offered ? String(data.equity_offered) : "0",
    valuation: data.valuation ? String(data.valuation) : "0",
    valuationCurrency: data.valuation_currency || "$",
    revenue: data.revenue ? String(data.revenue) : "0",
    revenueCurrency: data.revenue_currency || "$",
    marketOpportunity: data.market_opportunity ? String(data.market_opportunity) : "0",
    marketOpportunityCurrency: data.market_opportunity_currency || "$",
    exchange: data.exchange,
    symbol: data.symbol,
    price: data.price?.toString() || "0",
    changePercent: data.change_percent?.toString() || "0",
    marketCap: data.marketcap?.toString() || "0",
    PERatio: data.pe_ratio?.toString() || "0",
    averageVolume: data.average_volume?.toString() || "0",
    targetPrice: data.target_price || 0,
    targetPriceChange: data.target_price_change_percentage || undefined,
    priceTargetDisclaimer: data.price_target_disclaimer || "Price targets are analyst estimates and should not be considered as investment advice.",
  };
  return (
    <div className="flex gap-[30px] md:gap-[50px] min-h-screen flex-col justify-center items-center antialiased pt-[73px] bg-[#070809]">
      <TitleSection {...titleSectionProps} />
      {showStocksSection &&  <StocksSection {...stocksSectionProps} />}
      <Footer slug={data.slug} disclaimer={data?.disclaimer || ''} />
      <StockRatingModal
        isOpen={isRatingModalOpen}
        onClose={() => setIsRatingModalOpen(false)}
        companyName={data.title}
        slug={data.slug}
        source={ratingModalSource}
      />
    </div>
  );
}

export default CustomShowcasePage;
