import { 
  NextRequest, NextResponse,
} from "next/server";

interface ButtonDistribution {
  watchlist: number;
  rateShowcase: number;
  total: number;
  watchlistPercentage: number;
  rateShowcasePercentage: number;
}

interface MixpanelEvent {
  properties: {
    buttonType?: string;
    slug?: string;
    [key: string]: any;
  };
}

export async function POST (request: NextRequest) {
  try {
    const { 
      from_date, to_date, slug,
    } = await request.json();

    const MIXPANEL_API_SECRET = process.env.MIXPANEL_API_SECRET;

    const auth = Buffer.from(`${MIXPANEL_API_SECRET}:`).toString("base64");

    let fromDate = from_date;
    const toDate = to_date || new Date().toISOString().split("T")[0];

    if (!fromDate) {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      fromDate = thirtyDaysAgo.toISOString().split("T")[0];
    }

    const exportUrl = `https://data-eu.mixpanel.com/api/2.0/export`;
    const exportParams = new URLSearchParams({
      from_date: fromDate,
      to_date: toDate,
      event: '["A/B Button Displayed"]',
    });

    const response = await fetch(`${exportUrl}?${exportParams}`, {
      method: "GET",
      headers: {
        Authorization: `Basic ${auth}`,
        Accept: "text/plain",
        "Content-Type": "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Mixpanel API error:", response.status, errorText);
      return NextResponse.json(
        { error: `Mixpanel API error: ${response.status} - ${errorText}` },
        { status: 500 },
      );
    }

    let responseText = "";
    try {
      if (!response.body) {
        throw new Error("No response body");
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      const chunks: string[] = [];
      let totalSize = 0;
      const maxSize = 50 * 1024 * 1024;

      while (true) {
        const { 
          done, value,
        } = await reader.read();

        if (done) {
          break;
        }
        const chunk = decoder.decode(value, { stream: true });
        totalSize += chunk.length;

        if (totalSize > maxSize) {
          break;
        }

        chunks.push(chunk);
      }

      chunks.push(decoder.decode());
      responseText = chunks.join("");
    } catch (textError) {
      console.error("Error reading response text:", textError);
      return NextResponse.json(
        { error: "Failed to read response from Mixpanel API" },
        { 
          status: 500,
        },
      );
    }

    const events: MixpanelEvent[] = [];

    try {
      if (responseText.trim()) {
        const lines = responseText.trim().split("\n");
        for (const line of lines) {
          try {
            if (line.trim()) {
              const event = JSON.parse(line);
              if (event.event === "A/B Button Displayed" && event.properties) {
                events.push({
                  properties: event.properties,
                });
              }
            }
          } catch (parseError) {
            console.warn("Failed to parse line:", line, parseError);
          }
        }
      }
    } catch (parseError) {
      console.error("Error parsing response:", parseError);
    }

    const distribution: ButtonDistribution = {
      watchlist: 0,
      rateShowcase: 0,
      total: 0,
      watchlistPercentage: 0,
      rateShowcasePercentage: 0,
    };

    events.forEach((event) => {
      if (slug && event.properties?.slug !== slug) {
        return;
      }

      const buttonType = event.properties?.buttonType?.toLowerCase();
      if (buttonType === "watchlist") {
        distribution.watchlist++;
      } else if (buttonType === "rate_showcase") {
        distribution.rateShowcase++;
      }
    });

    distribution.total = distribution.watchlist + distribution.rateShowcase;

    if (distribution.total > 0) {
      distribution.watchlistPercentage = Math.round(
        (distribution.watchlist / distribution.total) * 100,
      );
      distribution.rateShowcasePercentage = Math.round(
        (distribution.rateShowcase / distribution.total) * 100,
      );
    }

    return NextResponse.json(
      { distribution },
      {
        headers: {
          "Cache-Control":
            "no-store, no-cache, must-revalidate, proxy-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      },
    );
  } catch (error) {
    console.error("Error fetching button distribution:", error);
    return NextResponse.json(
      {
        error: `Failed to fetch button distribution: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      },
      { status: 500 },
    );
  }
}
